import { getCollection } from 'astro:content';
import type { ColoringPage, Category } from '../types/coloringPage';

// 辅助函数：获取分类信息
function getCategoryInfo(entry: any): Category {
  // 直接使用categoryInfo字段
  return entry.data.categoryInfo;
}

// 辅助函数：将 entry 转换为 ColoringPage 对象
function entryToColoringPage(entry: any): ColoringPage {
  const id = entry.data.id;
  const slug = entry.id.replace(/\.(md|mdx)$/, '');

  // 获取资源文件夹
  const assetFolder = entry.data.assetFolder;

  // 获取分类信息
  const categoryInfo = getCategoryInfo(entry);

  // 返回 ColoringPage 对象
  return {
    id: id,
    slug: slug,
    title: entry.data.title,
    categoryInfo: categoryInfo,

    // 资源文件夹
    assetFolder,

    // 其他字段
    dateAdded: entry.data.dateAdded,
    popular: entry.data.popular || false,
    featured: entry.data.featured || false,
    premium: entry.data.premium || false,
    description: entry.data.description || '',
    tags: entry.data.tags || [],
    collections: entry.data.collections || [],
  };
}

// 不再需要 ID 和 slug 之间的转换函数

// 获取所有着色页面
export async function getAllColoringPages(): Promise<ColoringPage[]> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages');

    // 将内容集合条目转换为 ColoringPage 类型
    return coloringPagesEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting coloring pages:', error);
    return [];
  }
}

// 按类别获取着色页面
export async function getColoringPagesByCategory(category: string): Promise<ColoringPage[]> {
  try {
    // 从 category-coloring-pages 格式中提取类别名称
    const categoryName = category.replace(/-coloring-pages$/, '').replace(/-/g, ' ');

    const coloringPagesEntries = await getCollection('coloring-pages', ({ data }) => {
      return data.categoryInfo.main.toLowerCase() === categoryName.toLowerCase();
    });

    return coloringPagesEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting coloring pages by category:', error);
    return [];
  }
}

// 按主分类和子分类获取着色页面
export async function getColoringPagesBySubcategory(mainCategory: string, subCategory?: string): Promise<ColoringPage[]> {
  try {
    // 从 category-coloring-pages 格式中提取主分类名称
    const mainCategoryName = mainCategory.replace(/-coloring-pages$/, '').replace(/-/g, ' ');

    // 如果有子分类，则从 main-sub 格式中提取子分类名称
    let subCategoryName: string | undefined = undefined;
    if (subCategory) {
      // 假设子分类格式为 main-sub，我们需要提取 sub 部分
      const parts = subCategory.split('-');
      // 子分类名称应该是最后一部分
      subCategoryName = parts[parts.length - 1].replace(/-/g, ' ');
    }

    const coloringPagesEntries = await getCollection('coloring-pages', ({ data }) => {
      // 获取分类信息
      const categoryInfo = data.categoryInfo;

      // 主分类必须匹配
      const mainMatches = categoryInfo.main.toLowerCase() === mainCategoryName.toLowerCase();

      // 如果指定了子分类，则子分类也必须匹配
      if (subCategoryName && categoryInfo.sub) {
        return mainMatches && categoryInfo.sub.toLowerCase() === subCategoryName.toLowerCase();
      }

      // 如果没有指定子分类，则只需匹配主分类
      return mainMatches;
    });

    return coloringPagesEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting coloring pages by subcategory:', error);
    return [];
  }
}

// 通过 ID 获取单个着色页面
export async function getColoringPageById(id: string): Promise<ColoringPage | undefined> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages');

    // 直接使用 id 匹配文件名（不带扩展名）或 data.id
    const entry = coloringPagesEntries.find(entry =>
      entry.id === `${id}.md` || entry.id === `${id}.mdx` || entry.data.id === id
    );

    if (!entry) return undefined;

    return entryToColoringPage(entry);
  } catch (error) {
    console.error('Error getting coloring page by ID:', error);
    return undefined;
  }
}

// 获取热门着色页面
export async function getPopularColoringPages(): Promise<ColoringPage[]> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages', ({ data }) => {
      return data.popular === true;
    });

    // 按添加日期排序（从新到旧）
    const sortedEntries = [...coloringPagesEntries].sort((a, b) => {
      const dateA = a.data.dateAdded ? new Date(a.data.dateAdded).getTime() : 0;
      const dateB = b.data.dateAdded ? new Date(b.data.dateAdded).getTime() : 0;
      return dateB - dateA; // 降序排列，最新的在前面
    });

    return sortedEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting popular coloring pages:', error);
    return [];
  }
}

// 获取带计数的类别
export async function getCategoriesWithCounts(): Promise<Array<{name: string, count: number, slug: string}>> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages');

    const categoryCounts: Record<string, number> = {};

    coloringPagesEntries.forEach(entry => {
      const categoryName = entry.data.categoryInfo.main;
      if (!categoryCounts[categoryName]) {
        categoryCounts[categoryName] = 0;
      }
      categoryCounts[categoryName]++;
    });

    return Object.entries(categoryCounts).map(([categoryName, count]) => ({
      name: categoryName,
      count,
      slug: `${categoryName.toLowerCase().replace(/\s+/g, '-')}-coloring-pages`,
    }));
  } catch (error) {
    console.error('Error getting categories with counts:', error);
    return [];
  }
}

// 定义二级分类结构
export interface CategoryWithSubcategories {
  name: string;       // 主分类名称
  slug: string;       // 主分类slug
  count: number;      // 主分类下的总页面数
  subcategories: Array<{
    name: string;     // 子分类名称
    slug: string;     // 子分类slug
    count: number;    // 子分类下的页面数
  }>;
}

// 获取带子分类的分类列表
export async function getCategoriesWithSubcategories(): Promise<CategoryWithSubcategories[]> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages');

    // 创建分类映射
    const categoryMap: Record<string, {
      count: number,
      subcategories: Record<string, number>
    }> = {};

    // 遍历所有页面，统计分类和子分类
    coloringPagesEntries.forEach(entry => {
      const categoryInfo = getCategoryInfo(entry);
      const mainCategory = categoryInfo.main;
      const subCategory = categoryInfo.sub;

      // 确保主分类存在
      if (!categoryMap[mainCategory]) {
        categoryMap[mainCategory] = {
          count: 0,
          subcategories: {}
        };
      }

      // 增加主分类计数
      categoryMap[mainCategory].count++;

      // 如果有子分类，增加子分类计数
      if (subCategory) {
        if (!categoryMap[mainCategory].subcategories[subCategory]) {
          categoryMap[mainCategory].subcategories[subCategory] = 0;
        }
        categoryMap[mainCategory].subcategories[subCategory]++;
      }
    });

    // 转换为数组格式
    return Object.entries(categoryMap).map(([name, data]) => {
      const subcategories = Object.entries(data.subcategories).map(([subName, count]) => ({
        name: subName,
        slug: `${name.toLowerCase().replace(/\s+/g, '-')}-${subName.toLowerCase().replace(/\s+/g, '-')}`,
        count
      }));

      return {
        name,
        slug: `${name.toLowerCase().replace(/\s+/g, '-')}-coloring-pages`,
        count: data.count,
        subcategories
      };
    });
  } catch (error) {
    console.error('Error getting categories with subcategories:', error);
    return [];
  }
}

// 获取最新添加的着色页面
export async function getNewColoringPages(limit: number = 10): Promise<ColoringPage[]> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages');

    // 按添加日期排序
    const sortedEntries = [...coloringPagesEntries].sort((a, b) => {
      const dateA = a.data.dateAdded ? new Date(a.data.dateAdded).getTime() : 0;
      const dateB = b.data.dateAdded ? new Date(b.data.dateAdded).getTime() : 0;
      return dateB - dateA; // 降序排列，最新的在前面
    });

    // 限制返回数量
    const limitedEntries = sortedEntries.slice(0, limit);

    return limitedEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting new coloring pages:', error);
    return [];
  }
}

// 按标签获取着色页面
export async function getColoringPagesByTag(tag: string): Promise<ColoringPage[]> {
  try {
    // 检查标签是否存在
    if (!tag) {
      console.error('Tag is undefined or empty');
      return [];
    }

    // 将连字符转换回空格，以便正确匹配标签
    const normalizedTag = tag.replace(/-/g, ' ');

    const coloringPagesEntries = await getCollection('coloring-pages', ({ data }) => {
      const pageTags = data.tags || [];
      return pageTags.some(pageTag =>
        pageTag.toLowerCase() === normalizedTag.toLowerCase()
      );
    });

    return coloringPagesEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting coloring pages by tag:', error);
    return [];
  }
}

// 获取所有标签及其计数
export async function getTagsWithCounts(): Promise<Array<{name: string, count: number, slug: string}>> {
  try {
    const coloringPagesEntries = await getCollection('coloring-pages');

    const tagCounts: Record<string, number> = {};

    // 统计每个标签的出现次数
    coloringPagesEntries.forEach(entry => {
      const tags = entry.data.tags || [];
      tags.forEach(tag => {
        if (!tagCounts[tag]) {
          tagCounts[tag] = 0;
        }
        tagCounts[tag]++;
      });
    });

    // 转换为数组并排序（按计数降序）
    return Object.entries(tagCounts)
      .map(([tag, count]) => ({
        name: tag,
        count,
        slug: tag.toLowerCase().replace(/\s+/g, '-'),
      }))
      .sort((a, b) => b.count - a.count);
  } catch (error) {
    console.error('Error getting tags with counts:', error);
    return [];
  }
}

// 获取有效标签（count > 1）的 slug 集合
export async function getValidTagSlugs(): Promise<Set<string>> {
  try {
    const tags = await getTagsWithCounts();
    const validTags = tags.filter(tag => tag.count > 1);
    return new Set(validTags.map(tag => tag.slug));
  } catch (error) {
    console.error('Error getting valid tag slugs:', error);
    return new Set();
  }
}

// 检查标签是否有效（是否有对应的页面）
export async function isValidTag(tagName: string): Promise<boolean> {
  try {
    const validSlugs = await getValidTagSlugs();
    const slug = tagName.toLowerCase().replace(/\s+/g, '-');
    return validSlugs.has(slug);
  } catch (error) {
    console.error('Error checking if tag is valid:', error);
    return false;
  }
}

// 根据标签获取相关着色页面
export async function getRelatedColoringPagesByTags(
  tags: string[] = [],
  currentSlug: string,
  limit: number = 4
): Promise<ColoringPage[]> {
  try {
    if (!tags || tags.length === 0) {
      // 如果没有标签，返回热门页面作为备选
      const popularPages = await getPopularColoringPages();
      return popularPages.filter(page => page.slug !== currentSlug).slice(0, limit);
    }

    const coloringPagesEntries = await getCollection('coloring-pages');

    // 过滤掉当前页面
    const otherPages = coloringPagesEntries.filter(entry =>
      entry.id.replace(/\.(md|mdx)$/, '') !== currentSlug
    );

    // 计算每个页面与当前页面标签的匹配度
    const pagesWithRelevance = otherPages.map(entry => {
      const entryTags = entry.data.tags || [];

      // 计算匹配的标签数量
      const matchingTags = entryTags.filter(tag =>
        tags.includes(tag)
      );

      const relevanceScore = matchingTags.length;

      return {
        entry,
        relevanceScore,
        matchingTags
      };
    });

    // 按相关性排序（匹配标签数量从多到少）
    const sortedPages = pagesWithRelevance
      .filter(item => item.relevanceScore > 0) // 只保留至少有一个匹配标签的页面
      .sort((a, b) => b.relevanceScore - a.relevanceScore);

    // 如果相关页面不足，添加一些热门页面补充
    let relatedPages = sortedPages.map(item => item.entry);

    if (relatedPages.length < limit) {
      const popularPages = await getPopularColoringPages();
      const additionalPages = popularPages.filter(popularPage =>
        !relatedPages.some(relatedPage => relatedPage.id.replace(/\.(md|mdx)$/, '') === popularPage.slug) &&
        popularPage.slug !== currentSlug
      );

      // 将 ColoringPage 转换为 CollectionEntry 格式并过滤掉 undefined 值
      const additionalEntries = additionalPages
        .map(page => {
          return coloringPagesEntries.find(entry =>
            entry.id.replace(/\.(md|mdx)$/, '') === page.slug
          );
        })
        .filter((entry): entry is typeof coloringPagesEntries[0] => entry !== undefined);

      relatedPages = [...relatedPages, ...additionalEntries];
    }

    // 限制返回数量
    const limitedEntries = relatedPages.slice(0, limit);

    return limitedEntries.map(entry => entryToColoringPage(entry));
  } catch (error) {
    console.error('Error getting related coloring pages by tags:', error);
    return [];
  }
}

