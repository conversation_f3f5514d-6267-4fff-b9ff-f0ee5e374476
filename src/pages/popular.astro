---
import Layout from '@/layouts/Layout.astro';
import LazyColoringCard from '@/components/LazyColoringCard.astro';
import IntersectionObserver from '@/components/IntersectionObserver.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getPopularColoringPages, getPopularColoringPagesCount } from '@/data/coloringPages';
import { PERFORMANCE_CONFIG } from '@/config/performance';
import '@/styles/infinite-scroll.css';

// 获取URL参数
const url = new URL(Astro.request.url);
const page = parseInt(url.searchParams.get('page') || '1');
const pageSize = PERFORMANCE_CONFIG.pagination.popularPages;

// 获取总数
const totalItems = await getPopularColoringPagesCount();

// 预渲染更多内容以支持懒加载显示
const preloadCount = Math.min(200, totalItems); // 最多预加载200个
const popularPagesWithUrls = await getPopularColoringPages(preloadCount, 0);

// 分组：每组4个卡片（一行）
const cardsPerRow = 4;
const initialRows = 12; // 初始显示12行（48个卡片）
const initialVisibleCount = initialRows * cardsPerRow;

// 计算总页数（用于传统分页，但现在主要用懒加载）
const totalPages = Math.ceil(totalItems / pageSize);

// 生成分页信息（简化版，主要用于数据传递）
const pagination = {
  currentPage: page,
  totalPages,
  totalItems,
  hasNext: page < totalPages,
  hasPrev: page > 1,
  nextPage: page < totalPages ? page + 1 : null,
  prevPage: page > 1 ? page - 1 : null,
};

// 生成页码数组
function generatePageNumbers(currentPage: number, totalPages: number): number[] {
  const maxVisible = 5;
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 3) {
    return [1, 2, 3, 4, 5];
  }

  if (currentPage >= totalPages - 2) {
    return [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  return [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2];
}

const pageNumbers = generatePageNumbers(pagination.currentPage, pagination.totalPages);
---

<Layout title="Popular Coloring Pages - PrintableColoringHub" description="Browse our most popular printable coloring pages. Download free PDF and PNG formats.">
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Popular Coloring Pages", isActive: true }
        ]}
      />
    </div>

    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
      <h1 class="mb-4 sm:mb-0">Popular Coloring Pages</h1>
      <div class="text-sm text-gray-600" id="page-info">
        Showing 1-{Math.min(initialVisibleCount, popularPagesWithUrls.length)} of {pagination.totalItems} pages
      </div>
    </div>

    <!-- 无限滚动容器 -->
    <div id="infinite-scroll-container" class="relative">
      <IntersectionObserver threshold={0.1} rootMargin="100px">
        <div id="coloring-pages-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {popularPagesWithUrls.map((coloringPage, index) => {
            // 计算是否应该初始显示
            const isInitiallyVisible = index < initialVisibleCount;
            const rowIndex = Math.floor(index / cardsPerRow);

            return (
              <div
                class={`lazy-reveal-card ${isInitiallyVisible ? 'visible' : 'hidden'}`}
                data-row={rowIndex}
                data-index={index}
              >
                <LazyColoringCard
                  id={coloringPage.id}
                  slug={coloringPage.slug}
                  title={coloringPage.title}
                  assetFolder={coloringPage.assetFolder}
                  categoryInfo={coloringPage.categoryInfo}
                  tags={coloringPage.tags}
                  isFree={!coloringPage.premium}
                  priority={index < 4}
                  index={index}
                />
              </div>
            );
          })}
        </div>
      </IntersectionObserver>

      <!-- 加载指示器 -->
      <div id="loading-indicator" class="hidden justify-center items-center py-8">
        <div class="flex items-center space-x-2 text-gray-600">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <span>Loading more pages...</span>
        </div>
      </div>

      <!-- 滚动触发器 -->
      <div
        id="scroll-trigger"
        class="h-4 opacity-0"
        data-total-cards={popularPagesWithUrls.length}
        data-initial-visible={initialVisibleCount}
        data-cards-per-row={cardsPerRow}
      ></div>
    </div>

    <!-- 回到顶部按钮 -->
    <button
      id="back-to-top"
      class="fixed bottom-6 right-6 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-hover transition-all duration-300 opacity-0 pointer-events-none z-50"
      aria-label="Back to top"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
      </svg>
    </button>

    <!-- 分页导航 -->
    {pagination.totalPages > 1 && (
      <nav class="mt-12 flex justify-center" aria-label="Pagination">
        <div class="flex items-center space-x-2">
          <!-- 上一页按钮 -->
          {pagination.hasPrev ? (
            <a
              href={`/popular?page=${pagination.prevPage}`}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              Previous
            </a>
          ) : (
            <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              Previous
            </span>
          )}

          <!-- 页码 -->
          <div class="flex items-center space-x-1">
            {pageNumbers.map((pageNum) => {
              const isActive = pageNum === pagination.currentPage;

              return isActive ? (
                <span class="px-3 py-2 text-sm font-medium text-white bg-primary border border-primary rounded-md">
                  {pageNum}
                </span>
              ) : (
                <a
                  href={`/popular?page=${pageNum}`}
                  class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
                >
                  {pageNum}
                </a>
              );
            })}
          </div>

          <!-- 下一页按钮 -->
          {pagination.hasNext ? (
            <a
              href={`/popular?page=${pagination.nextPage}`}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              Next
            </a>
          ) : (
            <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              Next
            </span>
          )}
        </div>
      </nav>
    )}
  </div>
</Layout>

<script>
  // 简单的懒加载显示管理器
  class LazyRevealManager {
    private visibleCount: number = 0;
    private totalCards: number = 0;
    private cardsPerRow: number = 4;
    private cards: NodeListOf<Element> | null = null;

    constructor() {
      this.init();
    }

    private init() {
      // 获取DOM元素和配置
      const scrollTrigger = document.getElementById('scroll-trigger');
      this.cards = document.querySelectorAll('.lazy-reveal-card');

      if (!scrollTrigger || !this.cards) return;

      // 从数据属性获取配置
      this.totalCards = parseInt(scrollTrigger.dataset.totalCards || '0');
      this.visibleCount = parseInt(scrollTrigger.dataset.initialVisible || '48');
      this.cardsPerRow = parseInt(scrollTrigger.dataset.cardsPerRow || '4');

      // 设置滚动监听
      this.setupScrollListener();
    }

    private setupScrollListener() {
      // 使用节流的滚动监听
      let ticking = false;

      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            this.checkScrollPosition();
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    private checkScrollPosition() {
      // 检查是否还有隐藏的卡片需要显示
      if (this.visibleCount >= this.totalCards) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // 距离底部200px时开始显示下一行
      const triggerDistance = 200;
      const shouldReveal = scrollTop + windowHeight >= documentHeight - triggerDistance;

      if (shouldReveal) {
        this.revealNextRow();
      }
    }

    private revealNextRow() {
      if (!this.cards || this.visibleCount >= this.totalCards) return;

      // 计算下一行的卡片范围
      const nextRowStart = this.visibleCount;
      const nextRowEnd = Math.min(nextRowStart + this.cardsPerRow, this.totalCards);

      // 显示下一行的卡片
      for (let i = nextRowStart; i < nextRowEnd; i++) {
        const card = this.cards[i] as HTMLElement;
        if (card && card.classList.contains('hidden')) {
          // 添加显示动画
          setTimeout(() => {
            card.classList.remove('hidden');
            card.classList.add('visible', 'revealing');

            // 动画完成后清理类
            setTimeout(() => {
              card.classList.remove('revealing');
            }, 600);
          }, (i - nextRowStart) * 150); // 每个卡片延迟150ms
        }
      }

      // 更新可见数量
      this.visibleCount = nextRowEnd;
      this.updatePageInfo();
    }

    private updatePageInfo() {
      // 更新页面信息显示
      const pageInfoElement = document.getElementById('page-info');
      if (pageInfoElement) {
        pageInfoElement.textContent = `Showing 1-${this.visibleCount} of ${this.totalCards} pages`;
      }
    }

    public destroy() {
      // 清理事件监听器
      window.removeEventListener('scroll', this.checkScrollPosition.bind(this));
    }
  }

  // 回到顶部按钮管理
  class BackToTopManager {
    private button: HTMLElement | null = null;
    private isVisible: boolean = false;

    constructor() {
      this.init();
    }

    private init() {
      this.button = document.getElementById('back-to-top');
      if (!this.button) return;

      // 添加点击事件
      this.button.addEventListener('click', this.scrollToTop.bind(this));

      // 监听滚动事件
      window.addEventListener('scroll', this.handleScroll.bind(this));
    }

    private handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const shouldShow = scrollTop > 300;

      if (shouldShow !== this.isVisible) {
        this.isVisible = shouldShow;
        this.toggleVisibility();
      }
    }

    private toggleVisibility() {
      if (!this.button) return;

      if (this.isVisible) {
        this.button.classList.remove('opacity-0', 'pointer-events-none');
        this.button.classList.add('opacity-100', 'pointer-events-auto');
      } else {
        this.button.classList.add('opacity-0', 'pointer-events-none');
        this.button.classList.remove('opacity-100', 'pointer-events-auto');
      }
    }

    private scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    public destroy() {
      if (this.button) {
        this.button.removeEventListener('click', this.scrollToTop.bind(this));
      }
      window.removeEventListener('scroll', this.handleScroll.bind(this));
    }
  }

  // 初始化管理器
  let lazyRevealManager: LazyRevealManager;
  let backToTopManager: BackToTopManager;

  document.addEventListener('DOMContentLoaded', () => {
    lazyRevealManager = new LazyRevealManager();
    backToTopManager = new BackToTopManager();
  });

  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    if (lazyRevealManager) {
      lazyRevealManager.destroy();
    }
    if (backToTopManager) {
      backToTopManager.destroy();
    }
  });
</script>
