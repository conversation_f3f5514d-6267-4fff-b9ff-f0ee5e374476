---
import Layout from '@/layouts/Layout.astro';
import LazyColoringCard from '@/components/LazyColoringCard.astro';
import IntersectionObserver from '@/components/IntersectionObserver.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getPopularColoringPages, getPopularColoringPagesCount } from '@/data/coloringPages';
import { PERFORMANCE_CONFIG } from '@/config/performance';
import '@/styles/infinite-scroll.css';

// 获取URL参数
const url = new URL(Astro.request.url);
const page = parseInt(url.searchParams.get('page') || '1');
const pageSize = PERFORMANCE_CONFIG.pagination.popularPages;

// 获取总数和当前页数据
const totalItems = await getPopularColoringPagesCount();
const totalPages = Math.ceil(totalItems / pageSize);

// 计算当前页的页面范围
const startIndex = (page - 1) * pageSize;
const endIndex = startIndex + pageSize;

// 只获取当前页需要的数据
const popularPagesWithUrls = await getPopularColoringPages(pageSize, startIndex);

// 生成分页信息
const pagination = {
  currentPage: page,
  totalPages,
  totalItems,
  hasNext: page < totalPages,
  hasPrev: page > 1,
  nextPage: page < totalPages ? page + 1 : null,
  prevPage: page > 1 ? page - 1 : null,
};

// 生成页码数组
function generatePageNumbers(currentPage: number, totalPages: number): number[] {
  const maxVisible = 5;
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 3) {
    return [1, 2, 3, 4, 5];
  }

  if (currentPage >= totalPages - 2) {
    return [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  return [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2];
}

const pageNumbers = generatePageNumbers(pagination.currentPage, pagination.totalPages);
---

<Layout title="Popular Coloring Pages - PrintableColoringHub" description="Browse our most popular printable coloring pages. Download free PDF and PNG formats.">
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Popular Coloring Pages", isActive: true }
        ]}
      />
    </div>

    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
      <h1 class="mb-4 sm:mb-0">Popular Coloring Pages</h1>
      <div class="text-sm text-gray-600">
        Showing {startIndex + 1}-{Math.min(endIndex, pagination.totalItems)} of {pagination.totalItems} pages
      </div>
    </div>

    <!-- 无限滚动容器 -->
    <div id="infinite-scroll-container" class="relative">
      <IntersectionObserver threshold={0.1} rootMargin="100px">
        <div id="coloring-pages-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {popularPagesWithUrls.map((coloringPage, index) => (
            <LazyColoringCard
              id={coloringPage.id}
              slug={coloringPage.slug}
              title={coloringPage.title}
              assetFolder={coloringPage.assetFolder}
              categoryInfo={coloringPage.categoryInfo}
              tags={coloringPage.tags}
              isFree={!coloringPage.premium}
              priority={index < 4}
              index={index}
            />
          ))}
        </div>
      </IntersectionObserver>

      <!-- 加载指示器 -->
      <div id="loading-indicator" class="hidden justify-center items-center py-8">
        <div class="flex items-center space-x-2 text-gray-600">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <span>Loading more pages...</span>
        </div>
      </div>

      <!-- 滚动触发器 -->
      <div id="scroll-trigger" class="h-4 opacity-0" data-current-page={pagination.currentPage} data-total-pages={pagination.totalPages}></div>
    </div>

    <!-- 回到顶部按钮 -->
    <button
      id="back-to-top"
      class="fixed bottom-6 right-6 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-hover transition-all duration-300 opacity-0 pointer-events-none z-50"
      aria-label="Back to top"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
      </svg>
    </button>

    <!-- 分页导航 -->
    {pagination.totalPages > 1 && (
      <nav class="mt-12 flex justify-center" aria-label="Pagination">
        <div class="flex items-center space-x-2">
          <!-- 上一页按钮 -->
          {pagination.hasPrev ? (
            <a
              href={`/popular?page=${pagination.prevPage}`}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              Previous
            </a>
          ) : (
            <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              Previous
            </span>
          )}

          <!-- 页码 -->
          <div class="flex items-center space-x-1">
            {pageNumbers.map((pageNum) => {
              const isActive = pageNum === pagination.currentPage;

              return isActive ? (
                <span class="px-3 py-2 text-sm font-medium text-white bg-primary border border-primary rounded-md">
                  {pageNum}
                </span>
              ) : (
                <a
                  href={`/popular?page=${pageNum}`}
                  class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
                >
                  {pageNum}
                </a>
              );
            })}
          </div>

          <!-- 下一页按钮 -->
          {pagination.hasNext ? (
            <a
              href={`/popular?page=${pagination.nextPage}`}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              Next
            </a>
          ) : (
            <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              Next
            </span>
          )}
        </div>
      </nav>
    )}
  </div>
</Layout>

<script>
  // 无限滚动实现 - 按行加载
  class InfiniteScrollManager {
    private loadedCount: number = 0;
    private totalItems: number = 0;
    private isLoading: boolean = false;
    private observer: IntersectionObserver | null = null;
    private grid: HTMLElement | null = null;
    private loadingIndicator: HTMLElement | null = null;
    private scrollTrigger: HTMLElement | null = null;
    private cardsPerRow: number = 4;
    private animationDelay: number = 150;

    constructor() {
      this.init();
    }

    private init() {
      // 获取DOM元素
      this.grid = document.getElementById('coloring-pages-grid');
      this.loadingIndicator = document.getElementById('loading-indicator');
      this.scrollTrigger = document.getElementById('scroll-trigger');

      if (!this.scrollTrigger) return;

      // 从数据属性获取信息
      const currentPage = parseInt(this.scrollTrigger.dataset.currentPage || '1');
      const totalPages = parseInt(this.scrollTrigger.dataset.totalPages || '1');

      // 计算已加载的卡片数量和总数
      this.loadedCount = (currentPage - 1) * 48 + (this.grid?.children.length || 0);
      this.totalItems = totalPages * 48;

      // 设置配置
      this.cardsPerRow = 4; // 每行4个卡片
      this.animationDelay = 150; // 动画延迟

      // 设置滚动监听而不是Intersection Observer
      this.setupScrollListener();
    }

    private setupScrollListener() {
      // 使用节流的滚动监听
      let ticking = false;

      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            this.checkScrollPosition();
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    private checkScrollPosition() {
      if (this.isLoading || this.loadedCount >= this.totalItems) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // 距离底部200px时开始加载
      const triggerDistance = 200;
      const shouldLoad = scrollTop + windowHeight >= documentHeight - triggerDistance;

      if (shouldLoad) {
        this.loadNextRow();
      }
    }

    private async loadNextRow() {
      if (this.isLoading || this.loadedCount >= this.totalItems) return;

      this.isLoading = true;
      this.showLoadingIndicator();

      try {
        const response = await fetch(`/api/popular-pages?offset=${this.loadedCount}&limit=${this.cardsPerRow}`);

        if (!response.ok) {
          throw new Error('Failed to load next row');
        }

        const data = await response.json();

        if (data.pages && data.pages.length > 0) {
          await this.appendRow(data.pages);
          this.loadedCount += data.pages.length;
        }
      } catch (error) {
        console.error('Error loading next row:', error);
        this.showErrorMessage();
      } finally {
        this.isLoading = false;
        this.hideLoadingIndicator();
      }
    }

    private async appendRow(pages: any[]) {
      if (!this.grid) return;

      // 一次只处理一行（最多4个卡片）
      const rowPages = pages.slice(0, this.cardsPerRow);

      for (let i = 0; i < rowPages.length; i++) {
        const page = rowPages[i];
        const cardElement = await this.createCardElement(page, this.loadedCount + i);

        // 设置初始状态（隐藏）
        cardElement.style.opacity = '0';
        cardElement.style.transform = 'translateY(20px)';
        cardElement.classList.add('newly-loaded');

        // 立即添加到DOM
        this.grid.appendChild(cardElement);

        // 延迟显示动画，每个卡片错开时间
        setTimeout(() => {
          cardElement.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
          cardElement.style.opacity = '1';
          cardElement.style.transform = 'translateY(0)';
          cardElement.classList.add('loaded');

          // 动画完成后清理样式
          setTimeout(() => {
            cardElement.style.transition = '';
            cardElement.classList.remove('newly-loaded');
          }, 600);
        }, i * this.animationDelay);
      }

      // 重新初始化懒加载观察器
      setTimeout(() => {
        if ((window as any).reinitIntersectionObserver) {
          (window as any).reinitIntersectionObserver();
        }
      }, rowPages.length * this.animationDelay + 600);

      // 更新页面信息显示
      this.updatePageInfo();
    }

    private async createCardElement(page: any, index: number): Promise<HTMLElement> {
      // 创建卡片容器
      const cardDiv = document.createElement('div');
      cardDiv.className = 'flex flex-col bg-white rounded-lg overflow-hidden transition-all duration-200 h-full relative border border-gray-200 shadow-sm hover:translate-y-[-2px] hover:shadow-md coloring-card lazy-load-item';

      // 设置数据属性
      cardDiv.setAttribute('data-category', page.categoryInfo?.main?.toLowerCase().replace(/\s+/g, '-') || '');
      cardDiv.setAttribute('data-subcategory', page.categoryInfo?.sub?.toLowerCase().replace(/\s+/g, '-') || '');
      cardDiv.setAttribute('data-tags', (page.tags || []).join(',').toLowerCase());

      // 构建卡片HTML
      const pageUrl = `/coloring-pages/${page.slug || page.id}`;
      const assetFolder = page.assetFolder;
      const title = page.title;
      const isFree = !page.premium;

      cardDiv.innerHTML = `
        <figure class="m-0 relative w-full">
          <a href="${pageUrl}" class="no-underline text-inherit block relative z-[5]">
            <div class="aspect-square overflow-hidden relative rounded group">
              <!-- 黑白图片（默认显示） -->
              <div class="w-full h-full transition-opacity duration-300 ease-in-out group-hover:opacity-0">
                <img
                  src="https://static.printablecoloringhub.com/${assetFolder}_monochrome.png?width=500&height=500&fit=cover&quality=80&format=auto&dpr=2"
                  alt="${title} coloring page"
                  loading="lazy"
                  class="w-full h-full object-cover"
                />
              </div>

              <!-- 彩色图片（悬停时显示） -->
              <div class="absolute inset-0 opacity-0 transition-opacity duration-300 ease-in-out delay-100 z-[8] group-hover:opacity-100">
                <img
                  src="https://static.printablecoloringhub.com/${assetFolder}_colored.png?width=500&height=500&fit=cover&quality=80&format=auto&dpr=2"
                  alt="${title} colored version"
                  loading="lazy"
                  class="w-full h-full object-cover"
                />
              </div>

              <!-- 悬停遮罩 -->
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 z-[9] flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div class="flex flex-col items-center space-y-2">
                  <button class="bg-white text-gray-800 px-3 py-1 rounded-full text-xs font-medium hover:bg-gray-100 transition-colors">
                    Color Reference
                  </button>
                </div>
              </div>

              <!-- 免费标签 -->
              ${isFree ? `
                <div class="absolute top-2 left-2 z-[10]">
                  <span class="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                    Free
                  </span>
                </div>
              ` : ''}
            </div>
          </a>
        </figure>

        <div class="p-4 flex-1 flex flex-col">
          <h3 class="font-semibold text-gray-900 mb-2 line-clamp-1 leading-tight">
            <a href="${pageUrl}" class="hover:text-primary transition-colors">
              ${title}
            </a>
          </h3>

          <!-- 标签 -->
          ${page.tags && page.tags.length > 0 ? `
            <div class="flex flex-wrap gap-1 mb-3">
              ${page.tags.slice(0, 3).map((tag: string) => `
                <span class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                  ${tag}
                </span>
              `).join('')}
              ${page.tags.length > 3 ? `
                <span class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                  +${page.tags.length - 3}
                </span>
              ` : ''}
            </div>
          ` : ''}

          <!-- 按钮区域 -->
          <div class="mt-auto flex flex-col space-y-2">
            <a href="${pageUrl}" class="btn btn-primary btn-sm text-center">
              View Details
            </a>
            <div class="flex space-x-2">
              <button onclick="downloadFile('https://static.printablecoloringhub.com/${assetFolder}_monochrome.pdf', '${title.replace(/'/g, "\\'")} - Coloring Page.pdf')"
                      class="btn btn-outline btn-sm flex-1 download-btn">
                PDF
              </button>
              <button onclick="downloadFile('https://static.printablecoloringhub.com/${assetFolder}_monochrome.png', '${title.replace(/'/g, "\\'")} - Coloring Page.png')"
                      class="btn btn-outline btn-sm flex-1 download-btn">
                PNG
              </button>
            </div>
          </div>
        </div>
      `;

      return cardDiv;
    }

    private showLoadingIndicator() {
      if (this.loadingIndicator) {
        this.loadingIndicator.classList.remove('hidden');
        this.loadingIndicator.classList.add('flex');
      }
    }

    private hideLoadingIndicator() {
      if (this.loadingIndicator) {
        this.loadingIndicator.classList.add('hidden');
        this.loadingIndicator.classList.remove('flex');
      }
    }

    private showErrorMessage() {
      if (this.loadingIndicator) {
        this.loadingIndicator.innerHTML = `
          <div class="flex items-center space-x-2 text-red-600 error-message">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Failed to load more pages. Please try again.</span>
            <button onclick="location.reload()" class="ml-2 px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200 transition-colors">
              Retry
            </button>
          </div>
        `;
      }
    }

    private updatePageInfo() {
      // 更新页面信息显示
      const pageInfoElement = document.querySelector('.text-sm.text-gray-600');
      if (pageInfoElement) {
        const currentCount = this.grid?.children.length || 0;
        pageInfoElement.textContent = `Showing 1-${currentCount} of ${this.totalItems} pages`;
      }
    }

    public destroy() {
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  }

  // 回到顶部按钮管理
  class BackToTopManager {
    private button: HTMLElement | null = null;
    private isVisible: boolean = false;

    constructor() {
      this.init();
    }

    private init() {
      this.button = document.getElementById('back-to-top');
      if (!this.button) return;

      // 添加点击事件
      this.button.addEventListener('click', this.scrollToTop.bind(this));

      // 监听滚动事件
      window.addEventListener('scroll', this.handleScroll.bind(this));
    }

    private handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const shouldShow = scrollTop > 300;

      if (shouldShow !== this.isVisible) {
        this.isVisible = shouldShow;
        this.toggleVisibility();
      }
    }

    private toggleVisibility() {
      if (!this.button) return;

      if (this.isVisible) {
        this.button.classList.remove('opacity-0', 'pointer-events-none');
        this.button.classList.add('opacity-100', 'pointer-events-auto');
      } else {
        this.button.classList.add('opacity-0', 'pointer-events-none');
        this.button.classList.remove('opacity-100', 'pointer-events-auto');
      }
    }

    private scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    public destroy() {
      if (this.button) {
        this.button.removeEventListener('click', this.scrollToTop.bind(this));
      }
      window.removeEventListener('scroll', this.handleScroll.bind(this));
    }
  }

  // 初始化管理器
  let infiniteScrollManager: InfiniteScrollManager;
  let backToTopManager: BackToTopManager;

  document.addEventListener('DOMContentLoaded', () => {
    infiniteScrollManager = new InfiniteScrollManager();
    backToTopManager = new BackToTopManager();
  });

  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    if (infiniteScrollManager) {
      infiniteScrollManager.destroy();
    }
    if (backToTopManager) {
      backToTopManager.destroy();
    }
  });
</script>
