import type { APIRoute } from 'astro';
import { getPopularColoringPages } from '@/data/coloringPages';
import { PERFORMANCE_CONFIG } from '@/config/performance';

export const GET: APIRoute = async ({ url }) => {
  try {
    // 获取查询参数
    const searchParams = new URLSearchParams(url.search);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = PERFORMANCE_CONFIG.pagination.popularPages;

    // 验证页码
    if (page < 1) {
      return new Response(
        JSON.stringify({ error: 'Invalid page number' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    // 获取数据
    const pages = await getPopularColoringPages(pageSize, offset);

    // 返回JSON响应
    return new Response(
      JSON.stringify({
        pages,
        currentPage: page,
        pageSize,
        hasMore: pages.length === pageSize,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300', // 缓存5分钟
        },
      }
    );
  } catch (error) {
    console.error('Error in popular-pages API:', error);
    
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
